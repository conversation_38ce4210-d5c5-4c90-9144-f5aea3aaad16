# ===== 进入项目目录 =====
cd /var/www/novel-app

# ===== 创建环境配置文件 =====
cat > server/.env << 'EOF'
NODE_ENV=production
PORT=5000

# 数据库配置
DB_HOST=localhost
DB_USER=novel_user
DB_PASSWORD=NovelPassword123!
DB_NAME=novel_db

# JWT密钥
JWT_SECRET=your_super_secret_jwt_key_2024_change_this

# CORS配置
CORS_ORIGIN=*
EOF

# ===== 安装后端依赖 =====
cd server
npm install

# ===== 初始化数据库 =====
node config/init-db.js

# ===== 返回项目根目录 =====
cd ..

# ===== 安装前端依赖 =====
npm install

# ===== 构建前端项目 =====
npm run build

# ===== 创建日志目录 =====
mkdir -p logs

# ===== 使用PM2启动后端服务 =====
pm2 start server/server.js --name "novel-backend"

# ===== 设置PM2开机自启 =====
pm2 startup
pm2 save

# ===== 配置Nginx =====
# 备份原配置
cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup

# 创建网站配置
cat > /etc/nginx/conf.d/novel-app.conf << 'EOF'
server {
    listen 80;
    server_name _;

    # 前端静态文件
    location / {
        root /var/www/novel-app/build;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # API代理到后端
    location /api/ {
        proxy_pass http://localhost:5000/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# ===== 启动Nginx =====
systemctl start nginx
systemctl enable nginx

# ===== 检查服务状态 =====
echo "=== 检查服务状态 ==="
pm2 status
systemctl status nginx --no-pager
systemctl status mysqld --no-pager

echo "=== 部署完成！==="
echo "访问地址: http://$(curl -s ifconfig.me)"
