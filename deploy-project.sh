#!/bin/bash

echo "=== 项目部署脚本 ==="

PROJECT_DIR="/var/www/novel-app"
cd $PROJECT_DIR

# 1. 上传项目文件（您需要先将项目文件上传到服务器）
echo "1. 确保项目文件已上传到 $PROJECT_DIR"

# 2. 安装后端依赖
echo "2. 安装后端依赖..."
cd server
npm install

# 3. 安装前端依赖
echo "3. 安装前端依赖..."
cd ..
npm install

# 4. 构建前端项目
echo "4. 构建前端项目..."
npm run build

# 5. 配置MySQL数据库
echo "5. 配置数据库..."
mysql -u root -p << EOF
CREATE DATABASE IF NOT EXISTS novel_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'novel_user'@'localhost' IDENTIFIED BY 'your_password_here';
GRANT ALL PRIVILEGES ON novel_db.* TO 'novel_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
EOF

# 6. 初始化数据库
echo "6. 初始化数据库..."
cd server
node config/init-db.js

# 7. 使用PM2启动后端服务
echo "7. 启动后端服务..."
pm2 start server.js --name "novel-backend"

# 8. 安装并配置Nginx
echo "8. 配置Nginx..."
yum install -y nginx
systemctl start nginx
systemctl enable nginx

echo "=== 部署完成 ==="
echo "后端API: http://您的IP:5000"
echo "前端访问: http://您的IP"
