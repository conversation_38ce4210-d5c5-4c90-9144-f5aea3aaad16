#!/bin/bash

echo "=== 阿里云服务器环境配置脚本 ==="

# 更新系统
echo "1. 更新系统包..."
yum update -y

# 安装Node.js
echo "2. 安装Node.js..."
curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
yum install -y nodejs

# 验证安装
echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

# 安装MySQL
echo "3. 安装MySQL..."
yum install -y mysql-server mysql
systemctl start mysqld
systemctl enable mysqld

# 安装Git
echo "4. 安装Git..."
yum install -y git

# 安装PM2（进程管理器）
echo "5. 安装PM2..."
npm install -g pm2

# 创建项目目录
echo "6. 创建项目目录..."
mkdir -p /var/www/novel-app
cd /var/www/novel-app

# 设置防火墙规则
echo "7. 配置防火墙..."
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --add-port=5000/tcp
firewall-cmd --permanent --add-port=3000/tcp
firewall-cmd --reload

echo "=== 环境配置完成 ==="
echo "请继续执行项目部署步骤"
