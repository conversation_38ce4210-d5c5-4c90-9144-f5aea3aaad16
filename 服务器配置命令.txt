# ===== 第一步：更新系统 =====
yum update -y

# ===== 第二步：安装Node.js =====
# 添加Node.js官方仓库
curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -

# 安装Node.js和npm
yum install -y nodejs

# 验证安装
node --version
npm --version

# ===== 第三步：安装MySQL =====
# 下载MySQL仓库
wget https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm

# 安装MySQL仓库
rpm -ivh mysql80-community-release-el7-3.noarch.rpm

# 安装MySQL服务器
yum install -y mysql-server

# 启动MySQL服务
systemctl start mysqld
systemctl enable mysqld

# 查找临时密码
grep 'temporary password' /var/log/mysqld.log

# ===== 第四步：安装其他必要软件 =====
# 安装Git
yum install -y git

# 安装Nginx
yum install -y nginx

# 安装PM2（Node.js进程管理器）
npm install -g pm2

# 安装压缩工具
yum install -y unzip

# ===== 第五步：创建项目目录 =====
mkdir -p /var/www/novel-app
cd /var/www/novel-app

# ===== 第六步：配置防火墙 =====
# 启动防火墙服务
systemctl start firewalld
systemctl enable firewalld

# 添加端口规则
firewall-cmd --permanent --add-port=22/tcp
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --add-port=5000/tcp

# 重新加载防火墙规则
firewall-cmd --reload

# 查看开放的端口
firewall-cmd --list-ports

echo "环境配置完成！"
