# ===== 常见问题解决方案 =====

## 问题1：无法连接SSH
解决方案：
1. 检查安全组是否开放22端口
2. 确认IP地址正确
3. 确认用户名密码正确

## 问题2：MySQL安装失败
解决方案：
# 清理MySQL相关包
yum remove mysql* -y
# 重新安装
yum install -y mysql-server

## 问题3：Node.js安装失败
解决方案：
# 手动下载安装
wget https://nodejs.org/dist/v18.17.0/node-v18.17.0-linux-x64.tar.xz
tar -xf node-v18.17.0-linux-x64.tar.xz
mv node-v18.17.0-linux-x64 /usr/local/node
echo 'export PATH=/usr/local/node/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

## 问题4：前端构建失败
解决方案：
# 增加内存限制
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build

## 问题5：PM2启动失败
解决方案：
# 查看详细错误
pm2 logs novel-backend
# 手动启动测试
cd /var/www/novel-app/server
node server.js

## 问题6：Nginx无法启动
解决方案：
# 检查配置文件语法
nginx -t
# 查看错误日志
tail -f /var/log/nginx/error.log

## 问题7：数据库连接失败
解决方案：
# 检查MySQL服务
systemctl status mysqld
# 测试数据库连接
mysql -u novel_user -p novel_db

## 问题8：防火墙问题
解决方案：
# 临时关闭防火墙测试
systemctl stop firewalld
# 如果可以访问，则是防火墙问题，重新配置规则

## 问题9：权限问题
解决方案：
# 设置正确的文件权限
chown -R root:root /var/www/novel-app
chmod -R 755 /var/www/novel-app

## 问题10：端口被占用
解决方案：
# 查看端口占用
netstat -tlnp | grep :5000
# 杀死占用进程
kill -9 进程ID
