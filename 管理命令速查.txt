# ===== 日常管理命令 =====

## PM2进程管理
pm2 status                    # 查看所有进程状态
pm2 restart novel-backend     # 重启后端服务
pm2 stop novel-backend        # 停止后端服务
pm2 start novel-backend       # 启动后端服务
pm2 logs novel-backend        # 查看后端日志
pm2 logs novel-backend --lines 100  # 查看最近100行日志

## Nginx管理
systemctl status nginx        # 查看Nginx状态
systemctl restart nginx       # 重启Nginx
systemctl start nginx         # 启动Nginx
systemctl stop nginx          # 停止Nginx
nginx -t                      # 测试配置文件语法
nginx -s reload               # 重新加载配置

## MySQL管理
systemctl status mysqld       # 查看MySQL状态
systemctl restart mysqld      # 重启MySQL
mysql -u root -p              # 登录MySQL
mysql -u novel_user -p novel_db  # 登录应用数据库

## 系统监控
top                           # 查看系统资源使用
htop                          # 更友好的系统监控（需要安装）
df -h                         # 查看磁盘使用情况
free -h                       # 查看内存使用情况
netstat -tlnp                 # 查看端口监听情况

## 日志查看
tail -f /var/log/nginx/access.log    # 实时查看Nginx访问日志
tail -f /var/log/nginx/error.log     # 实时查看Nginx错误日志
tail -f /var/log/mysqld.log          # 查看MySQL日志
journalctl -u nginx -f               # 查看Nginx系统日志

## 文件操作
cd /var/www/novel-app         # 进入项目目录
ls -la                        # 查看文件列表
nano filename                 # 编辑文件（简单编辑器）
vi filename                   # 编辑文件（高级编辑器）

## 更新项目
cd /var/www/novel-app
# 备份当前版本
cp -r . ../novel-app-backup-$(date +%Y%m%d)
# 上传新文件后重新构建
npm run build
pm2 restart novel-backend

## 数据库备份
mysqldump -u novel_user -p novel_db > backup_$(date +%Y%m%d).sql

## 数据库恢复
mysql -u novel_user -p novel_db < backup_20240101.sql
