@echo off
echo 正在打包项目文件...

REM 创建临时目录
mkdir temp_deploy 2>nul

REM 复制项目文件（排除不需要的文件夹）
xcopy /E /I /Y src temp_deploy\src
xcopy /E /I /Y server temp_deploy\server
xcopy /E /I /Y public temp_deploy\public

REM 复制配置文件
copy package.json temp_deploy\
copy package-lock.json temp_deploy\ 2>nul

REM 复制部署脚本
copy deploy-setup.sh temp_deploy\ 2>nul
copy deploy-project.sh temp_deploy\ 2>nul
copy nginx.conf temp_deploy\ 2>nul
copy ecosystem.config.js temp_deploy\ 2>nul

echo 项目文件已准备完成，请将 temp_deploy 文件夹上传到服务器
pause
